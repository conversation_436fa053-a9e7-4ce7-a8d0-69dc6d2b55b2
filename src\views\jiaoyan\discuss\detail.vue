<template>
    <div class="bg">
        <div class="w1280">
            <back>返回</back>
            <!-- 讨论详情 -->
            <div class="boxItem">
                <div class="title">{{ detailData.title  }}</div>
                <div class="topBox">
                    <div class="userAndtime">
                        <div class="userName">{{ detailData.name  }}</div>
                        <div class="time">{{ detailData.createTime  }}</div>
                    </div>
                    <div class="rightBtn">
                        <div class="btn" @click="toggleLike">
                            <img :src="detailData.isCaseLiked ? '/src/assets/dianzan_sel.png' : '/src/assets/dianzan_def.png'" alt="" class="btnImg">
                            <div class="number">{{ detailData.caseLikedNumber || 0 }}</div>
                        </div>
                        <div class="btn" @click="replyTopic">
                            <img src="@/assets/pinglun_def.png" alt="" class="btnImg">
                            <div class="number">{{ detailData.number || 0 }}</div>
                        </div>
                        <div class="btn" v-if="detailData.isCreateAdd" @click="deleteTopic">
                            <img src="@/assets/delete.png" alt="" class="btnImg">
                        </div>
                    </div>
                </div>
                <div class="content" v-if="detailData.content">{{ detailData.content }}</div>
                <div class="imgBox" v-if="detailData.courseTopicList && detailData.courseTopicList.length > 0">
                    <img v-for="(item, index) in detailData.courseTopicList" :key="index" :src="item.url" alt="" class="imgSize">
                </div>
            </div>

            <!-- 排序 -->
            <div class="flex">
                <div class="leftPX">
                    <div class="xuText">排序:</div>
                    <div class="paixu">
                        <el-select
                            v-model="value"
                            clearable
                            placeholder="排序方式"
                            class="select"
                        >
                        <el-option
                            v-for="item in options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                            />
                        </el-select>
                    </div>
                </div>
                <div class="total">共<span class="replyNumber">{{ replyList.length }}</span>条回复</div>
            </div>
            <!-- 回复列表 -->
            <div class="boxItem" v-for="reply in replyList" :key="reply.id">
                <div class="topBox">
                    <div class="userAndtime">
                        <div class="userName">{{ reply.name }}</div>
                        <div class="time">{{ reply.createTime }}</div>
                    </div>
                    <div class="rightBtn">
                        <div class="btn" @click="toggleReplyLike(reply)">
                            <img :src="reply.isCaseLiked ? '/src/assets/dianzan_sel.png' : '/src/assets/dianzan_def.png'" alt="" class="btnImg">
                            <div class="number">{{ reply.caseLikedNumber || 0 }}</div>
                        </div>
                        <div class="btn" @click="openReplyDialog(reply.name, reply.id)">
                            <img src="@/assets/pinglun_def.png" alt="" class="btnImg">
                            <div class="number">{{ reply.number || 0 }}</div>
                        </div>
                        <div class="btn" v-if="reply.isCreateAdd" @click="deleteReply(reply.id)">
                            <img src="@/assets/delete.png" alt="" class="btnImg">
                        </div>
                    </div>
                </div>
                <div class="replyContent" v-if="reply.content">{{ reply.content }}</div>
                <div class="imgBox" v-if="reply.fileInfoEntityList && reply.fileInfoEntityList.length > 0">
                    <img v-for="(file, index) in reply.fileInfoEntityList" :key="index" :src="file.url" alt="" class="imgSize">
                </div>
                <!-- 子回复暂时保留原有结构，后续可根据需要调整 -->
                <div class="replyBox" v-if="false">
                    <div class="topBox">
                        <div class="userAndtime">
                            <div class="userName">子回复用户</div>
                            <div class="time">时间</div>
                        </div>
                        <div class="rightBtn">
                            <div class="btn">
                                <img src="@/assets/delete.png" alt="" class="btnImg">
                            </div>
                        </div>
                    </div>
                    <div class="replyContent">子回复内容</div>
                </div>
            </div>
        </div>

        <!-- 回复话题弹窗 -->
        <ReplyTopic
            ref="replyTopicRef"
            :researchOfficeId="researchOfficeId"
            :parentId="parentId"
            @refreshList="refreshList"
        />

        <!-- 回复评论弹窗 -->
        <ReplyDialog
            v-model:visible="replyDialogVisible"
            :parentId="currentReplyId"
            :replyName="currentReplyName"
            @refresh="refreshList"
        />
    </div>

</template>
    
<script setup>
import {getMessageDetails} from '@/api/study.js'
import { useRoute } from 'vue-router'
import ReplyTopic from './replyTopic.vue'
import ReplyDialog from './ReplyDialog.vue'

const route = useRoute()
const replyTopicRef = ref()

const value = ref('')
const options = [
  {
    value: '时间顺序',
    label: '时间顺序',
  },
  {
    value: '时间倒序',
    label: '时间倒序',
  },
  {
    value: '点赞数',
    label: '点赞数',
  },
]

const researchOfficeId = ref(route.query.id )
const parentId = ref('0')
const replyDialogVisible = ref(false)
const currentReplyId = ref('')
const currentReplyName = ref('')

// 详情数据
const detailData = ref({})

// 回复列表数据
const replyList = ref([])

const replyTopic = () => {
  replyTopicRef.value.dialogVisible = true
}

const openReplyDialog = (replyName, replyId) => {
  currentReplyName.value = replyName
  currentReplyId.value = replyId
  replyDialogVisible.value = true
}

const refreshList = () => {
  console.log('刷新列表')
  loadData()
}

const loadData = () => {
    getMessageDetails({
        id: route.query.id
    }).then(res => {
        console.log(res)
        if (res.status === 0) {
            if (Array.isArray(res.data)) {
                replyList.value = res.data
            } else {
                // 如果返回的是对象，说明是详情数据
                detailData.value = res.data
                // 如果详情数据中包含回复列表
                if (res.data.courseTopicList && Array.isArray(res.data.courseTopicList)) {
                    replyList.value = res.data.courseTopicList
                }
            }
        }
    })
}

// 点赞/取消点赞
const toggleLike = () => {
    // 这里需要调用点赞接口
    console.log('点赞操作')
    // 临时切换状态，实际应该调用接口后更新
    detailData.value.isCaseLiked = detailData.value.isCaseLiked ? 0 : 1
    detailData.value.caseLikedNumber += detailData.value.isCaseLiked ? 1 : -1
}

// 回复点赞/取消点赞
const toggleReplyLike = (reply) => {
    // 这里需要调用回复点赞接口
    console.log('回复点赞操作', reply.id)
    // 临时切换状态，实际应该调用接口后更新
    reply.isCaseLiked = reply.isCaseLiked ? 0 : 1
    reply.caseLikedNumber += reply.isCaseLiked ? 1 : -1
}

// 删除话题
const deleteTopic = () => {
    // 这里需要调用删除接口
    console.log('删除话题')
    // 可以添加确认弹窗
}

// 删除回复
const deleteReply = (replyId) => {
    // 这里需要调用删除回复接口
    console.log('删除回复', replyId)
    // 可以添加确认弹窗
}

onMounted(() => {
    loadData()
});
</script>
      
<style lang="scss" scoped>
.bg {
  background: #f5f7fa;
  padding: 31px;
}
.title{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    color: #2D2F33;
    margin-bottom: 20px;
}
.boxItem{
    min-height: 149px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    box-sizing: border-box;
    padding: 30px;
    margin-bottom: 20px;
    margin-top: 20px;
}
.topBox{
    display: flex;
    justify-content: space-between;
}
.userAndtime{
    display: flex;
    align-items: center;
}
.userName{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 14px;
    color: #43474D;
    margin-right: 16px;
}
.time{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 13px;
    color: #878D99;
    margin-top: 4px;
}
.rightBtn{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.btn{
    display: flex;
    align-items: center;
    margin-right: 60px;
    cursor: pointer;
}
.btn:last-child{
    margin-right: 0px;
}
.btnImg{
    width: 24px;
    height: 24px;
    margin-right: 8px;
}
.number{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #878D99;
}
.content{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #44474D;
    line-height: 28px;
    margin-top: 12px;
}
.imgBox{
    display: grid;
    gap: 16px;
    grid-template-columns: repeat(5, 1fr);
    margin-top: 16px;
}
.imgSize{
    width: 180px;
    height: 135px;
    border-radius: 8px 8px 8px 8px;
}
.imgSize:last-child{
    margin-right: 0;
}
.flex{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.leftPX{
    display: flex;
    align-items: center;
}
.xuText{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #44474D;
    margin-right: 5px;
}
.paixu{
    display: flex;
    align-items: center;
}
.total{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #878D99;
}
.replyNumber{
    color:#2D2F33;
}
.replyContent{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #44474D;
    line-height: 28px;
}


.replyBox{
    min-height: 94px;
    background: #F5F7FA;
    border-radius: 8px 8px 8px 8px;
    margin-top: 16px;
    padding: 20px;
}

</style>
<style lang="scss">
.select {
    .el-select__wrapper{
        width: 180px;
        height: 40px;
        background: #FFFFFF;
        border-radius: 4px 4px 4px 4px;
    }
}
.el-select-dropdown__item {
    padding: 0 !important;
    padding-top: 0 !important;
}

.el-select-dropdown__item:hover {
    background-color: #386CFC !important;
    color: #FFFFFF !important;
}

</style>
  